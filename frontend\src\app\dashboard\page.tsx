'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/contexts/AuthContext';

export default function UserDashboard() {
  const { user } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState({
    totalOrders: 0,
    cartItems: 0,
    wishlistItems: 0,
    totalSpent: 0
  });

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }

    if (user.role === 'admin') {
      router.push('/admin/dashboard');
      return;
    }

    // Fetch user stats
    const fetchStats = async () => {
      try {
        // This would be real API calls in production
        setStats({
          totalOrders: 12,
          cartItems: 3,
          wishlistItems: 8,
          totalSpent: 2450
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
      }
    };

    fetchStats();
  }, [user, router]);

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🔒</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-8">Please login to access your dashboard.</p>
          <Link href="/auth/login">
            <Button>Login</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg p-8 mb-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">
                Welcome back, {user.name}! 🌿
              </h1>
              <p className="text-green-100 text-lg">
                Continue your organic food journey with GruhaPaaka
              </p>
            </div>
            <div className="hidden md:block text-6xl opacity-50">
              🛒
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <div className="text-3xl mb-2">📦</div>
            <h3 className="text-2xl font-bold text-gray-900">{stats.totalOrders}</h3>
            <p className="text-gray-600">Total Orders</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <div className="text-3xl mb-2">🛒</div>
            <h3 className="text-2xl font-bold text-gray-900">{stats.cartItems}</h3>
            <p className="text-gray-600">Cart Items</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <div className="text-3xl mb-2">❤️</div>
            <h3 className="text-2xl font-bold text-gray-900">{stats.wishlistItems}</h3>
            <p className="text-gray-600">Wishlist Items</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <div className="text-3xl mb-2">💰</div>
            <h3 className="text-2xl font-bold text-gray-900">₹{stats.totalSpent}</h3>
            <p className="text-gray-600">Total Spent</p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Quick Actions</h2>
            <div className="space-y-4">
              <Link href="/products">
                <div className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                  <div className="text-2xl mr-4">🛍️</div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Browse Products</h3>
                    <p className="text-gray-600 text-sm">Discover new organic foods</p>
                  </div>
                </div>
              </Link>
              
              <Link href="/cart">
                <div className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                  <div className="text-2xl mr-4">🛒</div>
                  <div>
                    <h3 className="font-semibold text-gray-900">View Cart</h3>
                    <p className="text-gray-600 text-sm">Review items in your cart</p>
                  </div>
                </div>
              </Link>
              
              <Link href="/wishlist">
                <div className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                  <div className="text-2xl mr-4">❤️</div>
                  <div>
                    <h3 className="font-semibold text-gray-900">My Wishlist</h3>
                    <p className="text-gray-600 text-sm">View saved products</p>
                  </div>
                </div>
              </Link>
              
              <Link href="/orders">
                <div className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                  <div className="text-2xl mr-4">📋</div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Order History</h3>
                    <p className="text-gray-600 text-sm">Track your orders</p>
                  </div>
                </div>
              </Link>
            </div>
          </div>

          {/* Account Settings */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Account Settings</h2>
            <div className="space-y-4">
              <Link href="/profile">
                <div className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                  <div className="text-2xl mr-4">👤</div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Profile Settings</h3>
                    <p className="text-gray-600 text-sm">Update your personal information</p>
                  </div>
                </div>
              </Link>
              
              <Link href="/addresses">
                <div className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                  <div className="text-2xl mr-4">📍</div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Delivery Addresses</h3>
                    <p className="text-gray-600 text-sm">Manage your addresses</p>
                  </div>
                </div>
              </Link>
              
              <Link href="/payment-methods">
                <div className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                  <div className="text-2xl mr-4">💳</div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Payment Methods</h3>
                    <p className="text-gray-600 text-sm">Manage payment options</p>
                  </div>
                </div>
              </Link>
              
              <Link href="/notifications">
                <div className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                  <div className="text-2xl mr-4">🔔</div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Notifications</h3>
                    <p className="text-gray-600 text-sm">Manage your preferences</p>
                  </div>
                </div>
              </Link>
            </div>
          </div>
        </div>

        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Recent Orders</h2>
            <Link href="/orders">
              <Button variant="outline" size="sm">View All</Button>
            </Link>
          </div>
          
          <div className="space-y-4">
            {/* Sample orders - would be fetched from API */}
            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center">
                <div className="text-2xl mr-4">📦</div>
                <div>
                  <h3 className="font-semibold text-gray-900">Order #GP001234</h3>
                  <p className="text-gray-600 text-sm">Organic Turmeric Powder, Basmati Rice</p>
                  <p className="text-gray-500 text-xs">Ordered on Dec 15, 2024</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-semibold text-gray-900">₹448</p>
                <span className="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                  Delivered
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center">
                <div className="text-2xl mr-4">📦</div>
                <div>
                  <h3 className="font-semibold text-gray-900">Order #GP001235</h3>
                  <p className="text-gray-600 text-sm">Organic Almonds, Coconut Oil</p>
                  <p className="text-gray-500 text-xs">Ordered on Dec 18, 2024</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-semibold text-gray-900">₹1,298</p>
                <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                  Shipped
                </span>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
