# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules/
*/node_modules/
frontend/node_modules/
backend/node_modules/
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions


# Package lock files
package-lock.json
*/package-lock.json

# testing
/coverage

# next.js
.next/
/.next/
/out/
out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
