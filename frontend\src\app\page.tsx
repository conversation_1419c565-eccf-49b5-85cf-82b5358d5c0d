'use client';

import { useState, useEffect } from 'react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { Button } from '@/components/ui/Button';
import Link from 'next/link';

interface Product {
  _id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  images: Array<{
    url: string;
    alt: string;
    isPrimary: boolean;
  }>;
  category: {
    name: string;
    image: string;
  };
  rating: number;
  reviewCount: number;
  weight: string;
  origin: string;
  isFeatured: boolean;
  inStock: boolean;
}

interface Category {
  _id: string;
  name: string;
  description: string;
  image: string;
  slug: string;
}

export default function Home() {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch featured products
        const productsResponse = await fetch('http://localhost:5000/api/products?featured=true&limit=8');
        const productsData = await productsResponse.json();
        
        // Fetch categories
        const categoriesResponse = await fetch('http://localhost:5000/api/categories');
        const categoriesData = await categoriesResponse.json();
        
        if (productsData.success) {
          setFeaturedProducts(productsData.data);
        }
        
        if (categoriesData.success) {
          setCategories(categoriesData.data);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="relative bg-gradient-to-r from-green-600 via-green-700 to-emerald-600 text-white overflow-hidden">
          <div className="absolute inset-0 bg-black opacity-20"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-green-600/90 to-emerald-600/90"></div>
          
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
            <div className="text-center">
              <div className="flex justify-center mb-8">
                <div className="bg-white/20 rounded-full p-6">
                  <span className="text-6xl md:text-8xl">🌿</span>
                </div>
              </div>
              
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
                Welcome to <span className="text-green-200">GruhaPaaka</span>
              </h1>
              
              <p className="text-lg md:text-xl mb-4 text-green-100 font-medium">
                Premium Organic Foods for Healthy Living
              </p>
              
              <p className="text-xl md:text-2xl mb-12 max-w-4xl mx-auto leading-relaxed">
                Discover the finest collection of <strong>organic foods</strong>, spices, and healthy products. 
                Fresh, natural, and sourced directly from <strong>certified organic farms</strong> across India.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Link href="/products">
                  <Button size="lg" className="bg-white text-green-600 hover:bg-green-50 px-8 py-4 text-lg font-semibold shadow-lg transform hover:scale-105 transition-all duration-200">
                    🛒 Shop Organic Foods
                  </Button>
                </Link>
                <Link href="/categories">
                  <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-green-600 px-8 py-4 text-lg font-semibold shadow-lg transform hover:scale-105 transition-all duration-200">
                    🌾 Browse Categories
                  </Button>
                </Link>
              </div>
            </div>
          </div>
          
          {/* Decorative elements */}
          <div className="absolute top-10 left-10 text-green-200 opacity-50">
            <span className="text-4xl">🌱</span>
          </div>
          <div className="absolute top-20 right-20 text-green-200 opacity-50">
            <span className="text-3xl">🍃</span>
          </div>
          <div className="absolute bottom-10 left-20 text-green-200 opacity-50">
            <span className="text-5xl">🌾</span>
          </div>
          <div className="absolute bottom-20 right-10 text-green-200 opacity-50">
            <span className="text-4xl">🥜</span>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Why Choose GruhaPaaka?
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                We're committed to bringing you the purest, healthiest organic foods
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center p-6 rounded-lg bg-green-50 hover:bg-green-100 transition-colors">
                <div className="text-5xl mb-4">🌱</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">100% Organic</h3>
                <p className="text-gray-600">Certified organic products from trusted farms</p>
              </div>
              
              <div className="text-center p-6 rounded-lg bg-green-50 hover:bg-green-100 transition-colors">
                <div className="text-5xl mb-4">🚚</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Fresh Delivery</h3>
                <p className="text-gray-600">Farm-fresh products delivered to your doorstep</p>
              </div>
              
              <div className="text-center p-6 rounded-lg bg-green-50 hover:bg-green-100 transition-colors">
                <div className="text-5xl mb-4">💚</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Healthy Living</h3>
                <p className="text-gray-600">Promoting wellness through natural nutrition</p>
              </div>
            </div>
          </div>
        </section>

        {/* Categories Section */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Shop by Category
              </h2>
              <p className="text-xl text-gray-600">
                Explore our wide range of organic food categories
              </p>
            </div>
            
            {loading ? (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="bg-white rounded-lg p-6 shadow-md animate-pulse">
                    <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {categories.slice(0, 8).map((category) => (
                  <Link key={category._id} href={`/categories/${category.slug}`}>
                    <div className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow cursor-pointer group">
                      <div className="text-4xl mb-4 text-center group-hover:scale-110 transition-transform">
                        {category.image}
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 text-center mb-2">
                        {category.name}
                      </h3>
                      <p className="text-sm text-gray-600 text-center">
                        {category.description}
                      </p>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </div>
        </section>

        {/* Featured Products Section */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Featured Organic Products
              </h2>
              <p className="text-xl text-gray-600">
                Handpicked premium organic foods for your healthy lifestyle
              </p>
            </div>
            
            {loading ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="bg-white rounded-lg shadow-md p-4 animate-pulse">
                    <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded mb-2"></div>
                    <div className="h-6 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {featuredProducts.map((product) => (
                  <Link key={product._id} href={`/products/${product._id}`}>
                    <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden group">
                      <div className="relative h-48 bg-gray-100">
                        <img
                          src={product.images[0]?.url || '/placeholder-product.jpg'}
                          alt={product.images[0]?.alt || product.name}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        {product.originalPrice && (
                          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-md text-sm font-semibold">
                            {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
                          </div>
                        )}
                      </div>
                      
                      <div className="p-4">
                        <p className="text-sm text-green-600 font-medium mb-1">
                          {product.category.name}
                        </p>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                          {product.name}
                        </h3>
                        <div className="flex items-center mb-2">
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <span
                                key={i}
                                className={`text-sm ${
                                  i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300'
                                }`}
                              >
                                ★
                              </span>
                            ))}
                          </div>
                          <span className="ml-2 text-sm text-gray-600">
                            ({product.reviewCount})
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span className="text-xl font-bold text-gray-900">
                              ₹{product.price}
                            </span>
                            {product.originalPrice && (
                              <span className="text-sm text-gray-500 line-through">
                                ₹{product.originalPrice}
                              </span>
                            )}
                          </div>
                          <span className="text-sm text-gray-500">
                            {product.weight}
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
            
            <div className="text-center mt-12">
              <Link href="/products">
                <Button size="lg" className="px-8 py-3">
                  View All Products
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Newsletter Section */}
        <section className="py-16 bg-green-600 text-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Stay Updated with GruhaPaaka
            </h2>
            <p className="text-xl mb-8">
              Get the latest updates on new organic products and healthy recipes
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-green-300"
              />
              <Button className="bg-white text-green-600 hover:bg-gray-100 px-6 py-3">
                Subscribe
              </Button>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
