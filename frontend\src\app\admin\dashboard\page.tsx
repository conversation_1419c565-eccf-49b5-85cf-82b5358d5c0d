'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/contexts/AuthContext';

export default function AdminDashboard() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState({
    totalOrders: 0,
    totalProducts: 0,
    totalUsers: 0,
    totalRevenue: 0,
    pendingOrders: 0,
    lowStockProducts: 0
  });

  useEffect(() => {
    if (!user) {
      router.push('/admin/login');
    } else if (user.role !== 'admin') {
      router.push('/');
    }
  }, [user, router]);

  useEffect(() => {
    if (user && user.role === 'admin') {
      fetchDashboardStats();
    }
  }, [user]);

  const fetchDashboardStats = async () => {
    try {
      // For now, use mock data
      setStats({
        totalOrders: 156,
        totalProducts: 89,
        totalUsers: 1234,
        totalRevenue: 456789,
        pendingOrders: 23,
        lowStockProducts: 8
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    }
  };

  const handleLogout = async () => {
    await logout();
    router.push('/admin/login');
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🔒</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Admin Access Required</h1>
          <p className="text-gray-600 mb-8">Please login as an admin to access this dashboard.</p>
          <Link href="/admin/login">
            <Button className="bg-red-600 hover:bg-red-700">Admin Login</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (user.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">⛔</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-8">You don't have permission to access the admin dashboard.</p>
          <Link href="/">
            <Button>Go to Homepage</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Admin Header */}
      <header className="bg-white shadow-lg border-b-4 border-red-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-3xl">🌿</span>
                <div className="flex flex-col">
                  <h1 className="text-2xl font-bold text-red-600">GruhaPaaka</h1>
                  <span className="text-sm text-red-500 font-medium -mt-1">Admin Portal</span>
                </div>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm text-gray-600">Welcome back,</p>
                <p className="font-semibold text-gray-900">{user.name}</p>
              </div>
              <div className="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center text-white font-bold">
                {user.name.charAt(0).toUpperCase()}
              </div>
              <Button
                onClick={handleLogout}
                variant="outline"
                className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white"
              >
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Dashboard Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
          <p className="text-gray-600">Manage your GruhaPaaka organic food store</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-900">{stats.totalOrders}</p>
                <p className="text-gray-600 font-medium">Total Orders</p>
              </div>
              <div className="text-4xl text-blue-500">📦</div>
            </div>
            <div className="mt-4">
              <span className="text-sm text-green-600 font-medium">+12% from last month</span>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-900">{stats.totalProducts}</p>
                <p className="text-gray-600 font-medium">Products</p>
              </div>
              <div className="text-4xl text-green-500">🛍️</div>
            </div>
            <div className="mt-4">
              <span className="text-sm text-green-600 font-medium">+5 new this week</span>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-purple-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-900">{stats.totalUsers}</p>
                <p className="text-gray-600 font-medium">Customers</p>
              </div>
              <div className="text-4xl text-purple-500">👥</div>
            </div>
            <div className="mt-4">
              <span className="text-sm text-green-600 font-medium">+8% growth</span>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-gray-900">₹{stats.totalRevenue.toLocaleString()}</p>
                <p className="text-gray-600 font-medium">Revenue</p>
              </div>
              <div className="text-4xl text-yellow-500">💰</div>
            </div>
            <div className="mt-4">
              <span className="text-sm text-green-600 font-medium">+15% this month</span>
            </div>
          </div>
        </div>

        {/* Alert Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="text-3xl mr-4">⚠️</div>
              <div>
                <h3 className="text-lg font-semibold text-orange-800">Pending Orders</h3>
                <p className="text-orange-700">{stats.pendingOrders} orders need attention</p>
              </div>
            </div>
          </div>
          
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center">
              <div className="text-3xl mr-4">📉</div>
              <div>
                <h3 className="text-lg font-semibold text-red-800">Low Stock Alert</h3>
                <p className="text-red-700">{stats.lowStockProducts} products running low</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Product Management */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <div className="text-3xl mr-3">📦</div>
              <h3 className="text-xl font-semibold text-gray-900">Product Management</h3>
            </div>
            <div className="space-y-3">
              <Link href="/admin/products">
                <Button className="w-full justify-start bg-green-600 hover:bg-green-700" size="sm">
                  📋 View All Products
                </Button>
              </Link>
              <Link href="/admin/products/add">
                <Button className="w-full justify-start" variant="outline" size="sm">
                  ➕ Add New Product
                </Button>
              </Link>
              <Link href="/admin/categories">
                <Button className="w-full justify-start" variant="outline" size="sm">
                  🏷️ Manage Categories
                </Button>
              </Link>
            </div>
          </div>

          {/* Order Management */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <div className="text-3xl mr-3">📋</div>
              <h3 className="text-xl font-semibold text-gray-900">Order Management</h3>
            </div>
            <div className="space-y-3">
              <Link href="/admin/orders">
                <Button className="w-full justify-start bg-blue-600 hover:bg-blue-700" size="sm">
                  📦 All Orders
                </Button>
              </Link>
              <Link href="/admin/orders?status=pending">
                <Button className="w-full justify-start" variant="outline" size="sm">
                  ⏳ Pending Orders ({stats.pendingOrders})
                </Button>
              </Link>
              <Link href="/admin/orders?status=shipped">
                <Button className="w-full justify-start" variant="outline" size="sm">
                  🚚 Shipped Orders
                </Button>
              </Link>
            </div>
          </div>

          {/* Analytics & Settings */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <div className="text-3xl mr-3">⚙️</div>
              <h3 className="text-xl font-semibold text-gray-900">Analytics & Settings</h3>
            </div>
            <div className="space-y-3">
              <Link href="/admin/users">
                <Button className="w-full justify-start bg-purple-600 hover:bg-purple-700" size="sm">
                  👥 Manage Users
                </Button>
              </Link>
              <Link href="/admin/analytics">
                <Button className="w-full justify-start" variant="outline" size="sm">
                  📊 Sales Analytics
                </Button>
              </Link>
              <Link href="/admin/settings">
                <Button className="w-full justify-start" variant="outline" size="sm">
                  ⚙️ Store Settings
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
