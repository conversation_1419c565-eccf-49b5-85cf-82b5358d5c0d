'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { useAuth } from '@/contexts/AuthContext';

export default function AdminLoginPage() {
  const router = useRouter();
  const { login } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Try to login as admin
      const success = await login(formData.email, formData.password, 'admin');
      
      if (success) {
        // Redirect to admin dashboard
        router.push('/admin/dashboard');
      } else {
        setError('Invalid admin credentials. Please check your email and password.');
      }
    } catch (err) {
      setError('Admin login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-red-50 to-white flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <Link href="/" className="inline-block">
            <div className="flex items-center justify-center space-x-2">
              <span className="text-3xl">🌿</span>
              <div className="flex flex-col">
                <h1 className="text-3xl font-bold text-red-600">GruhaPaaka</h1>
                <span className="text-sm text-red-500 font-medium -mt-1">Admin Portal</span>
              </div>
            </div>
          </Link>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Admin Login
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Access the admin dashboard to manage your store
          </p>
        </div>

        {/* Admin Badge */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
          <div className="text-4xl mb-2">👨‍💼</div>
          <h3 className="text-lg font-semibold text-red-800">Administrator Access</h3>
          <p className="text-sm text-red-600">Authorized personnel only</p>
        </div>

        {/* Login Form */}
        <form className="mt-8 space-y-6 bg-white p-6 rounded-lg shadow-md" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <Input
              id="email"
              name="email"
              type="email"
              label="Admin Email"
              placeholder="Enter admin email"
              value={formData.email}
              onChange={handleInputChange}
              required
            />
            <Input
              id="password"
              name="password"
              type="password"
              label="Admin Password"
              placeholder="Enter admin password"
              value={formData.password}
              onChange={handleInputChange}
              required
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm text-center bg-red-50 p-3 rounded-lg">
              {error}
            </div>
          )}

          <Button
            type="submit"
            className="w-full bg-red-600 hover:bg-red-700 focus:ring-red-500"
            disabled={isLoading}
          >
            {isLoading ? 'Signing in...' : 'Admin Sign In'}
          </Button>
        </form>

        {/* Demo Admin Credentials */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-red-800 mb-2">Demo Admin Credentials:</h4>
          <div className="text-xs text-red-700">
            <div><strong>Admin:</strong> <EMAIL> / admin123</div>
          </div>
        </div>

        {/* Back to Main Site */}
        <div className="text-center">
          <Link href="/" className="text-sm text-gray-600 hover:text-gray-800">
            ← Back to main website
          </Link>
        </div>
      </div>
    </div>
  );
}
