const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const User = require('../models/User');
const Category = require('../models/Category');
const Product = require('../models/Product');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/gruhapaaka');
    console.log('🌱 MongoDB Connected for GruhaPaaka seeding...');
  } catch (error) {
    console.error('Database connection error:', error);
    process.exit(1);
  }
};

const seedUsers = async () => {
  try {
    await User.deleteMany({});

    const users = [
      {
        name: 'GruhaPaaka Admin',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin',
        phone: '+91-9876543210',
        isActive: true
      },
      {
        name: 'Customer User',
        email: '<EMAIL>',
        password: 'user123',
        role: 'user',
        phone: '+91-9876543211',
        isActive: true
      },
      {
        name: 'Priya Sharma',
        email: '<EMAIL>',
        password: 'password123',
        role: 'user',
        phone: '+91-9876543212',
        isActive: true
      }
    ];

    await User.insertMany(users);
    console.log('✅ GruhaPaaka users seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding users:', error);
  }
};

const seedCategories = async () => {
  try {
    await Category.deleteMany({});

    const categories = [
      {
        name: 'Organic Spices',
        description: 'Premium organic whole and ground spices',
        image: '🌶️',
        slug: 'organic-spices',
        sortOrder: 1
      },
      {
        name: 'Organic Grains & Pulses',
        description: 'Nutritious organic grains and pulses',
        image: '🌾',
        slug: 'organic-grains-pulses',
        sortOrder: 2
      },
      {
        name: 'Organic Oils & Ghee',
        description: 'Pure cold-pressed oils and organic ghee',
        image: '🫒',
        slug: 'organic-oils-ghee',
        sortOrder: 3
      },
      {
        name: 'Organic Herbs & Teas',
        description: 'Fresh organic herbs and herbal teas',
        image: '🌿',
        slug: 'organic-herbs-teas',
        sortOrder: 4
      },
      {
        name: 'Organic Dry Fruits & Nuts',
        description: 'Premium organic dry fruits and nuts',
        image: '🥜',
        slug: 'organic-dry-fruits-nuts',
        sortOrder: 5
      },
      {
        name: 'Organic Sweeteners',
        description: 'Natural organic sweeteners and jaggery',
        image: '🍯',
        slug: 'organic-sweeteners',
        sortOrder: 6
      }
    ];

    const savedCategories = await Category.insertMany(categories);
    console.log('✅ GruhaPaaka categories seeded successfully');
    return savedCategories;
  } catch (error) {
    console.error('❌ Error seeding categories:', error);
    return [];
  }
};

const seedProducts = async (categories) => {
  try {
    await Product.deleteMany({});

    const products = [
      // Organic Spices
      {
        name: 'Organic Turmeric Powder',
        description: 'Pure organic turmeric powder with high curcumin content. Sourced directly from organic farms in Tamil Nadu. Known for its anti-inflammatory properties.',
        price: 149,
        originalPrice: 179,
        category: categories.find(c => c.name === 'Organic Spices')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=500',
            alt: 'Organic Turmeric Powder',
            isPrimary: true
          }
        ],
        weight: '500g',
        origin: 'Tamil Nadu, India',
        stockQuantity: 100,
        rating: 4.9,
        reviewCount: 256,
        tags: ['turmeric', 'organic', 'curcumin', 'anti-inflammatory'],
        isFeatured: true
      },
      {
        name: 'Organic Red Chili Powder',
        description: 'Premium organic red chili powder made from sun-dried Kashmiri chilies. Perfect heat and vibrant color for your dishes.',
        price: 199,
        originalPrice: 229,
        category: categories.find(c => c.name === 'Organic Spices')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1583049254466-c7d4b1b3b5b5?w=500',
            alt: 'Organic Red Chili Powder',
            isPrimary: true
          }
        ],
        weight: '500g',
        origin: 'Kashmir, India',
        stockQuantity: 75,
        rating: 4.7,
        reviewCount: 189,
        tags: ['chili', 'organic', 'kashmiri', 'spicy'],
        isFeatured: true
      },
      {
        name: 'Organic Garam Masala',
        description: 'Traditional blend of organic whole spices including cardamom, cinnamon, cloves, and black pepper. Freshly ground for maximum flavor.',
        price: 249,
        originalPrice: 299,
        category: categories.find(c => c.name === 'Organic Spices')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=500',
            alt: 'Organic Garam Masala',
            isPrimary: true
          }
        ],
        weight: '200g',
        origin: 'Rajasthan, India',
        stockQuantity: 60,
        rating: 4.8,
        reviewCount: 145,
        tags: ['garam masala', 'organic', 'spice blend', 'traditional'],
        isFeatured: true
      },

      // Organic Grains & Pulses
      {
        name: 'Organic Basmati Rice',
        description: 'Premium aged organic basmati rice with long grains and aromatic fragrance. Grown in the foothills of Himalayas.',
        price: 299,
        originalPrice: 349,
        category: categories.find(c => c.name === 'Organic Grains & Pulses')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=500',
            alt: 'Organic Basmati Rice',
            isPrimary: true
          }
        ],
        weight: '1kg',
        origin: 'Punjab, India',
        stockQuantity: 120,
        rating: 4.9,
        reviewCount: 312,
        tags: ['basmati', 'organic', 'rice', 'aromatic'],
        isFeatured: true
      },
      {
        name: 'Organic Moong Dal',
        description: 'Split yellow moong dal (mung beans) rich in protein and easy to digest. Perfect for daily cooking and baby food.',
        price: 179,
        originalPrice: 199,
        category: categories.find(c => c.name === 'Organic Grains & Pulses')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1599909533730-8b9e1c4b1e7a?w=500',
            alt: 'Organic Moong Dal',
            isPrimary: true
          }
        ],
        weight: '1kg',
        origin: 'Rajasthan, India',
        stockQuantity: 90,
        rating: 4.6,
        reviewCount: 178,
        tags: ['moong dal', 'organic', 'protein', 'pulses'],
        isFeatured: false
      },

      // Organic Oils & Ghee
      {
        name: 'Organic Cold-Pressed Coconut Oil',
        description: 'Virgin organic coconut oil extracted using traditional cold-press method. Rich in MCTs and perfect for cooking and skincare.',
        price: 399,
        originalPrice: 449,
        category: categories.find(c => c.name === 'Organic Oils & Ghee')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=500',
            alt: 'Organic Coconut Oil',
            isPrimary: true
          }
        ],
        weight: '500ml',
        origin: 'Kerala, India',
        stockQuantity: 80,
        rating: 4.8,
        reviewCount: 234,
        tags: ['coconut oil', 'organic', 'cold-pressed', 'virgin'],
        isFeatured: true
      },

      // Organic Herbs & Teas
      {
        name: 'Organic Tulsi Green Tea',
        description: 'Premium blend of organic green tea with holy basil (tulsi). Rich in antioxidants and known for its immunity-boosting properties.',
        price: 299,
        originalPrice: 349,
        category: categories.find(c => c.name === 'Organic Herbs & Teas')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500',
            alt: 'Organic Tulsi Green Tea',
            isPrimary: true
          }
        ],
        weight: '100g (50 tea bags)',
        origin: 'Darjeeling, India',
        stockQuantity: 70,
        rating: 4.7,
        reviewCount: 167,
        tags: ['tulsi', 'green tea', 'organic', 'immunity'],
        isFeatured: false
      },

      // Organic Dry Fruits & Nuts
      {
        name: 'Organic Almonds',
        description: 'Premium organic almonds from California. Rich in vitamin E, healthy fats, and protein. Perfect for snacking and cooking.',
        price: 899,
        originalPrice: 999,
        category: categories.find(c => c.name === 'Organic Dry Fruits & Nuts')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1508747703725-************?w=500',
            alt: 'Organic Almonds',
            isPrimary: true
          }
        ],
        weight: '500g',
        origin: 'California, USA',
        stockQuantity: 50,
        rating: 4.9,
        reviewCount: 289,
        tags: ['almonds', 'organic', 'nuts', 'protein'],
        isFeatured: true
      },

      // Organic Sweeteners
      {
        name: 'Organic Jaggery Powder',
        description: 'Pure organic jaggery powder made from sugarcane juice. Natural sweetener rich in iron and minerals. Chemical-free processing.',
        price: 149,
        originalPrice: 169,
        category: categories.find(c => c.name === 'Organic Sweeteners')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1609501676725-7186f734b2e1?w=500',
            alt: 'Organic Jaggery Powder',
            isPrimary: true
          }
        ],
        weight: '500g',
        origin: 'Maharashtra, India',
        stockQuantity: 110,
        rating: 4.6,
        reviewCount: 198,
        tags: ['jaggery', 'organic', 'natural sweetener', 'iron'],
        isFeatured: false
      }
    ];

    await Product.insertMany(products);
    console.log('✅ GruhaPaaka products seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding products:', error);
  }
};

const seedGruhaPaakaDatabase = async () => {
  try {
    await connectDB();
    
    console.log('🌱 Starting GruhaPaaka database seeding...');
    
    await seedUsers();
    const categories = await seedCategories();
    await seedProducts(categories);
    
    console.log('🎉 GruhaPaaka database seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding GruhaPaaka database:', error);
    process.exit(1);
  }
};

// Run seeder if called directly
if (require.main === module) {
  seedGruhaPaakaDatabase();
}

module.exports = { seedGruhaPaakaDatabase, seedUsers, seedCategories, seedProducts };
