const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const User = require('../models/User');
const Category = require('../models/Category');
const Product = require('../models/Product');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/gruhapaaka');
    console.log('MongoDB Connected for seeding...');
  } catch (error) {
    console.error('Database connection error:', error);
    process.exit(1);
  }
};

const seedUsers = async () => {
  try {
    // Clear existing users
    await User.deleteMany({});

    const users = [
      {
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Admin',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin',
        phone: '+91-9876543210',
        isActive: true
      },
      {
        name: 'Customer User',
        email: '<EMAIL>',
        password: 'user123',
        role: 'user',
        phone: '+91-9876543211',
        isActive: true
      },
      {
        name: '<PERSON>riya <PERSON>',
        email: '<EMAIL>',
        password: 'password123',
        role: 'user',
        phone: '+91-9876543212',
        isActive: true
      },
      {
        name: 'Rajesh Kumar',
        email: '<EMAIL>',
        password: 'password123',
        role: 'user',
        phone: '+91-9876543213',
        isActive: true
      }
    ];

    await User.insertMany(users);
    console.log('✅ Users seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding users:', error);
  }
};

const seedCategories = async () => {
  try {
    // Clear existing categories
    await Category.deleteMany({});

    const categories = [
      {
        name: 'Organic Spices',
        description: 'Premium organic whole and ground spices',
        image: '🌶️',
        slug: 'organic-spices',
        sortOrder: 1
      },
      {
        name: 'Organic Grains & Pulses',
        description: 'Nutritious organic grains and pulses',
        image: '🌾',
        slug: 'organic-grains-pulses',
        sortOrder: 2
      },
      {
        name: 'Organic Oils & Ghee',
        description: 'Pure cold-pressed oils and organic ghee',
        image: '🫒',
        slug: 'organic-oils-ghee',
        sortOrder: 3
      },
      {
        name: 'Organic Herbs & Teas',
        description: 'Fresh organic herbs and herbal teas',
        image: '🌿',
        slug: 'organic-herbs-teas',
        sortOrder: 4
      },
      {
        name: 'Organic Dry Fruits & Nuts',
        description: 'Premium organic dry fruits and nuts',
        image: '🥜',
        slug: 'organic-dry-fruits-nuts',
        sortOrder: 5
      },
      {
        name: 'Organic Sweeteners',
        description: 'Natural organic sweeteners and jaggery',
        image: '🍯',
        slug: 'organic-sweeteners',
        sortOrder: 6
      },
      {
        name: 'Organic Flours',
        description: 'Stone-ground organic flours',
        image: '🌾',
        slug: 'organic-flours',
        sortOrder: 7
      },
      {
        name: 'Organic Ready-to-Cook',
        description: 'Organic ready-to-cook mixes and masalas',
        image: '🍛',
        slug: 'organic-ready-to-cook',
        sortOrder: 8
      }
    ];

    const savedCategories = await Category.insertMany(categories);
    console.log('✅ Categories seeded successfully');
    return savedCategories;
  } catch (error) {
    console.error('❌ Error seeding categories:', error);
    return [];
  }
};

const seedProducts = async (categories) => {
  try {
    // Clear existing products
    await Product.deleteMany({});

    const products = [
      // Organic Spices
      {
        name: 'Organic Turmeric Powder',
        description: 'Pure organic turmeric powder with high curcumin content. Sourced directly from organic farms in Tamil Nadu.',
        price: 149,
        originalPrice: 179,
        category: categories.find(c => c.name === 'Organic Spices')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=500',
            alt: 'Organic Turmeric Powder',
            isPrimary: true
          }
        ],
        weight: '500g',
        origin: 'Tamil Nadu, India',
        stockQuantity: 100,
        rating: 4.9,
        reviewCount: 256,
        tags: ['turmeric', 'organic', 'curcumin', 'anti-inflammatory'],
        isFeatured: true
      },
      {
        name: 'Organic Red Chili Powder',
        description: 'Premium organic red chili powder made from sun-dried Kashmiri chilies. Perfect heat and color for your dishes.',
        price: 199,
        originalPrice: 229,
        category: categories.find(c => c.name === 'Organic Spices')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1583049254466-c7d4b1b3b5b5?w=500',
            alt: 'Organic Red Chili Powder',
            isPrimary: true
          }
        ],
        weight: '500g',
        origin: 'Kashmir, India',
        stockQuantity: 75,
        rating: 4.7,
        reviewCount: 189,
        tags: ['chili', 'organic', 'kashmiri', 'spicy'],
        isFeatured: true
      },
      {
        name: 'Ceylon Cinnamon Sticks',
        description: 'Authentic Ceylon cinnamon sticks with sweet, delicate flavor. Superior to regular cassia cinnamon.',
        price: 18.99,
        originalPrice: 22.99,
        category: categories.find(c => c.name === 'Whole Spices')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500',
            alt: 'Ceylon Cinnamon Sticks',
            isPrimary: true
          }
        ],
        weight: '50g',
        origin: 'Sri Lanka',
        stockQuantity: 75,
        rating: 4.9,
        reviewCount: 89,
        tags: ['cinnamon', 'ceylon', 'whole spice', 'premium'],
        isFeatured: true
      },
      // Ground Spices
      {
        name: 'Turmeric Powder',
        description: 'Pure turmeric powder with high curcumin content. Essential for Indian cooking and health benefits.',
        price: 12.99,
        category: categories.find(c => c.name === 'Ground Spices')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=500',
            alt: 'Turmeric Powder',
            isPrimary: true
          }
        ],
        weight: '200g',
        origin: 'Tamil Nadu, India',
        stockQuantity: 100,
        rating: 4.7,
        reviewCount: 156,
        tags: ['turmeric', 'ground spice', 'curcumin', 'health'],
        isFeatured: false
      },
      {
        name: 'Paprika Powder',
        description: 'Sweet Hungarian paprika powder with vibrant color and mild flavor.',
        price: 15.99,
        category: categories.find(c => c.name === 'Ground Spices')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1599909533730-8b9e1c4b1e7a?w=500',
            alt: 'Paprika Powder',
            isPrimary: true
          }
        ],
        weight: '100g',
        origin: 'Hungary',
        stockQuantity: 60,
        rating: 4.6,
        reviewCount: 78,
        tags: ['paprika', 'ground spice', 'hungarian', 'sweet'],
        isFeatured: false
      },
      // Spice Blends
      {
        name: 'Garam Masala Blend',
        description: 'Traditional Indian spice blend with cardamom, cinnamon, cloves, and more. Perfect for curries.',
        price: 16.99,
        category: categories.find(c => c.name === 'Spice Blends')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=500',
            alt: 'Garam Masala Blend',
            isPrimary: true
          }
        ],
        weight: '100g',
        origin: 'India',
        stockQuantity: 80,
        rating: 4.8,
        reviewCount: 203,
        tags: ['garam masala', 'spice blend', 'indian', 'curry'],
        isFeatured: true
      },
      // Herbs
      {
        name: 'Dried Oregano',
        description: 'Premium Mediterranean oregano with intense flavor. Perfect for pizza, pasta, and Mediterranean dishes.',
        price: 9.99,
        category: categories.find(c => c.name === 'Herbs')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500',
            alt: 'Dried Oregano',
            isPrimary: true
          }
        ],
        weight: '50g',
        origin: 'Greece',
        stockQuantity: 90,
        rating: 4.5,
        reviewCount: 67,
        tags: ['oregano', 'herb', 'mediterranean', 'dried'],
        isFeatured: false
      },
      // Seeds
      {
        name: 'Cumin Seeds',
        description: 'Whole cumin seeds with earthy, warm flavor. Essential for Indian, Mexican, and Middle Eastern cuisine.',
        price: 11.99,
        category: categories.find(c => c.name === 'Seeds')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=500',
            alt: 'Cumin Seeds',
            isPrimary: true
          }
        ],
        weight: '100g',
        origin: 'India',
        stockQuantity: 70,
        rating: 4.7,
        reviewCount: 112,
        tags: ['cumin', 'seeds', 'whole spice', 'earthy'],
        isFeatured: false
      },
      // Exotic Spices
      {
        name: 'Saffron Threads',
        description: 'Premium Kashmir saffron threads. The world\'s most expensive spice with unmatched flavor and aroma.',
        price: 89.99,
        originalPrice: 99.99,
        category: categories.find(c => c.name === 'Exotic Spices')._id,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1609501676725-7186f734b2e1?w=500',
            alt: 'Saffron Threads',
            isPrimary: true
          }
        ],
        weight: '2g',
        origin: 'Kashmir, India',
        stockQuantity: 25,
        rating: 5.0,
        reviewCount: 45,
        tags: ['saffron', 'exotic', 'premium', 'kashmir', 'luxury'],
        isFeatured: true
      }
    ];

    await Product.insertMany(products);
    console.log('✅ Products seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding products:', error);
  }
};

const seedDatabase = async () => {
  try {
    await connectDB();

    console.log('🌱 Starting database seeding...');

    await seedUsers();
    const categories = await seedCategories();
    await seedProducts(categories);

    console.log('🎉 Database seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
};

// Run seeder if called directly
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase, seedUsers, seedCategories, seedProducts };
