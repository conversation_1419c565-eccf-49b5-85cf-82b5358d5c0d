'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/contexts/AuthContext';
import { useCart } from '@/contexts/CartContext';
import { useWishlist } from '@/contexts/WishlistContext';

export const AmazonHeader = () => {
  const { user, logout } = useAuth();
  const { cartCount } = useCart();
  const { wishlistCount } = useWishlist();
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/products?search=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery('');
    }
  };

  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  return (
    <header className="bg-gray-900 text-white sticky top-0 z-50">
      {/* Main Header */}
      <div className="bg-gray-900">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center h-16">
            {/* Logo */}
            <Link href="/" className="flex items-center mr-6">
              <div className="bg-orange-400 text-black px-3 py-1 rounded font-bold text-xl">
                🌿 GruhaPaaka
              </div>
            </Link>

            {/* Deliver to */}
            <div className="hidden md:flex items-center mr-4 text-sm">
              <div className="mr-1">📍</div>
              <div>
                <div className="text-gray-300 text-xs">Deliver to</div>
                <div className="font-bold">India 110001</div>
              </div>
            </div>

            {/* Search Bar */}
            <div className="flex-1 max-w-2xl mx-4">
              <form onSubmit={handleSearch} className="flex">
                <select className="bg-gray-200 text-black px-3 py-2 rounded-l-md border-r border-gray-300 text-sm">
                  <option>All</option>
                  <option>Spices</option>
                  <option>Grains</option>
                  <option>Oils</option>
                  <option>Organic</option>
                </select>
                <input
                  type="text"
                  placeholder="Search GruhaPaaka"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1 px-4 py-2 text-black focus:outline-none"
                />
                <button
                  type="submit"
                  className="bg-orange-400 hover:bg-orange-500 px-4 py-2 rounded-r-md"
                >
                  <svg className="h-5 w-5 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
              </form>
            </div>

            {/* Language */}
            <div className="hidden md:flex items-center mr-4 text-sm">
              <span className="mr-1">🇮🇳</span>
              <span>EN</span>
            </div>

            {/* Account & Lists */}
            <div className="relative mr-4">
              {user ? (
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="text-sm hover:border border-white p-1 rounded"
                >
                  <div className="text-xs text-gray-300">Hello, {user.name}</div>
                  <div className="font-bold">Account & Lists ▼</div>
                </button>
              ) : (
                <Link href="/auth/login" className="text-sm hover:border border-white p-1 rounded">
                  <div className="text-xs text-gray-300">Hello, sign in</div>
                  <div className="font-bold">Account & Lists</div>
                </Link>
              )}

              {/* User Dropdown */}
              {isUserMenuOpen && user && (
                <div className="absolute right-0 mt-2 w-64 bg-white text-black rounded-lg shadow-lg border z-50">
                  <div className="p-4 border-b">
                    <p className="font-semibold">{user.name}</p>
                    <p className="text-sm text-gray-600">{user.email}</p>
                  </div>
                  
                  <div className="py-2">
                    <h3 className="px-4 py-2 font-semibold text-gray-800">Your Account</h3>
                    <Link href={user.role === 'admin' ? '/admin/dashboard' : '/dashboard'} 
                          className="block px-4 py-2 hover:bg-gray-100">
                      Your Dashboard
                    </Link>
                    <Link href="/orders" className="block px-4 py-2 hover:bg-gray-100">
                      Your Orders
                    </Link>
                    <Link href="/wishlist" className="block px-4 py-2 hover:bg-gray-100">
                      Your Wish List
                    </Link>
                    <Link href="/profile" className="block px-4 py-2 hover:bg-gray-100">
                      Your Account
                    </Link>
                  </div>
                  
                  <div className="border-t py-2">
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-red-600"
                    >
                      Sign Out
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Returns & Orders */}
            <Link href="/orders" className="hidden md:block text-sm mr-4 hover:border border-white p-1 rounded">
              <div className="text-xs text-gray-300">Returns</div>
              <div className="font-bold">& Orders</div>
            </Link>

            {/* Cart */}
            <Link href="/cart" className="flex items-center hover:border border-white p-1 rounded">
              <div className="relative mr-2">
                <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                </svg>
                {cartCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-orange-400 text-black text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                    {cartCount}
                  </span>
                )}
              </div>
              <span className="text-sm font-bold">Cart</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Navigation Bar */}
      <div className="bg-gray-800">
        <div className="max-w-7xl mx-auto px-4">
          <nav className="flex items-center h-10 space-x-6 text-sm">
            <button className="flex items-center hover:border border-white p-1 rounded">
              <svg className="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
              All
            </button>
            <Link href="/products" className="hover:border border-white p-1 rounded">Today's Deals</Link>
            <Link href="/categories" className="hover:border border-white p-1 rounded">Categories</Link>
            <Link href="/products?category=Organic Spices" className="hover:border border-white p-1 rounded">Spices</Link>
            <Link href="/products?category=Organic Grains" className="hover:border border-white p-1 rounded">Grains</Link>
            <Link href="/products?category=Organic Oils" className="hover:border border-white p-1 rounded">Oils & Ghee</Link>
            <Link href="/products?featured=true" className="hover:border border-white p-1 rounded">Best Sellers</Link>
            <Link href="/about" className="hover:border border-white p-1 rounded">About Us</Link>
            <Link href="/contact" className="hover:border border-white p-1 rounded">Customer Service</Link>
          </nav>
        </div>
      </div>
    </header>
  );
};
